[ERROR] 2025/07/04 21:34:23 handlers.go:150: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:23 handlers.go:154: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:159: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Stack Trace:
goroutine 574 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400040f4f0, 0x140002129a0, {0x140002526c0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400040f4f0, {0x103932bd8?, 0x140003043c0?}, {0x140002526c0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400033a1c0?, {0x103932bd8?, 0x140003043c0?}, {0x140002526c0?, 0x1400012e500?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1038daae0?, 0x1400033a1b0?, 0x1400022bc78?}, {0x1032b3126, 0x4}, {0x10430b480, 0x0, 0x102ebf7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1038daae0?, 0x1400033a1b0?, 0x1400022bce8?}, {0x10430b480?, 0x102769f00?, 0x1029faf70?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000687040, {0x1039435b0, 0x1400071b800}, {0x10430b480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 573
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:23 handlers.go:164: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:150: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:23 handlers.go:154: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:159: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Stack Trace:
goroutine 5042 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400060cb90, 0x140002da770, {0x14000370da0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400060cb90, {0x101ad2bd8?, 0x14000387bc0?}, {0x14000370da0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400066c130?, {0x101ad2bd8?, 0x14000387bc0?}, {0x14000370da0?, 0x140002d96c0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400066c120)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400066c120)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x101a7aae0?, 0x1400066c120?, 0x1400021dc78?}, {0x101453126, 0x4}, {0x1024ab480, 0x0, 0x10105f7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101a7aae0?, 0x1400066c120?, 0x100b71780?}, {0x1024ab480?, 0x1400021dd38?, 0x1008e7254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000605d60, {0x101ae35b0, 0x14000447680}, {0x1024ab480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 4993
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:23 handlers.go:164: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:56 handlers.go:150: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:56 handlers.go:154: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:56 handlers.go:159: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Stack Trace:
goroutine 358 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400003c030, 0x140006c8000, {0x140006a0020?, 0x8?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400003c030, {0x103622bd8?, 0x140000c0cc0?}, {0x140006a0020, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000da2e0?, {0x103622bd8?, 0x140000c0cc0?}, {0x140006a0020?, 0x140004407d0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000da2d0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000da2d0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1035caae0?, 0x140000da2d0?, 0x14000093c78?}, {0x102fa3126, 0x4}, {0x103ffb480, 0x0, 0x102baf7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1035caae0?, 0x140000da2d0?, 0x0?}, {0x103ffb480?, 0x14000093d38?, 0x102457028?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140004d0640, {0x1036335b0, 0x1400032fe60}, {0x103ffb480, 0x0, 0x102bdcb20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 357
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:56 handlers.go:164: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:36:37 handlers.go:150: TraceID: 0a846e60-b716-4f0b-b02a-d8bf5437b4c6 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:36:37 handlers.go:154: TraceID: 0a846e60-b716-4f0b-b02a-d8bf5437b4c6 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:36:37 handlers.go:159: TraceID: 0a846e60-b716-4f0b-b02a-d8bf5437b4c6 | Stack Trace:
goroutine 317 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000317120, 0x140005a4070, {0x140002c0140?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000317120, {0x10366ebd8?, 0x140005ab7d0?}, {0x140002c0140, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140001da490?, {0x10366ebd8?, 0x140005ab7d0?}, {0x140002c0140?, 0x140002ec130?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140001da480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140001da480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x103616ae0?, 0x140001da480?, 0x1400049ac78?}, {0x102fef126, 0x4}, {0x104047480, 0x0, 0x102bfb7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x103616ae0?, 0x140001da480?, 0x140001c2780?}, {0x104047480?, 0x1400049ad38?, 0x102483254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000513220, {0x10367f5b0, 0x14000385860}, {0x104047480, 0x0, 0x1?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 297
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:36:37 handlers.go:164: TraceID: 0a846e60-b716-4f0b-b02a-d8bf5437b4c6 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:37:56 handlers.go:150: TraceID: b2644e57-bf41-4767-a5a8-e4974d6e7792 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:37:56 handlers.go:154: TraceID: b2644e57-bf41-4767-a5a8-e4974d6e7792 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:37:56 handlers.go:159: TraceID: b2644e57-bf41-4767-a5a8-e4974d6e7792 | Stack Trace:
goroutine 316 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400029b7a0, 0x14000584380, {0x1400024c880?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400029b7a0, {0x10589abd8?, 0x140003ca240?}, {0x1400024c880, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000ece20?, {0x10589abd8?, 0x140003ca240?}, {0x1400024c880?, 0x140003c2d60?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000ece10)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x105842ae0?, 0x140000ece10?, 0x1400016cc78?}, {0x10521b126, 0x4}, {0x106273480, 0x0, 0x104e277e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105842ae0?, 0x140000ece10?, 0x1400016cdc8?}, {0x106273480?, 0x14000275380?, 0x1055e96a0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000444fa0, {0x1058ab5b0, 0x1400036fcb0}, {0x106273480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 315
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:37:56 handlers.go:164: TraceID: b2644e57-bf41-4767-a5a8-e4974d6e7792 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:37:56 handlers.go:150: TraceID: df0b947c-ce2f-4b08-a4ce-02f437c2007a | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:37:56 handlers.go:154: TraceID: df0b947c-ce2f-4b08-a4ce-02f437c2007a | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:37:56 handlers.go:159: TraceID: df0b947c-ce2f-4b08-a4ce-02f437c2007a | Stack Trace:
goroutine 284 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400029b7a0, 0x1400024ba40, {0x14000362760?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400029b7a0, {0x10589abd8?, 0x140003b5dd0?}, {0x14000362760, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000ece20?, {0x10589abd8?, 0x140003b5dd0?}, {0x14000362760?, 0x140003c2d60?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000ece10)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000ece10)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x105842ae0?, 0x140000ece10?, 0x140005c5c78?}, {0x10521b126, 0x4}, {0x106273480, 0x0, 0x104e277e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105842ae0?, 0x140000ece10?, 0x14000027ce8?}, {0x106273480?, 0x14000027d38?, 0x1046af254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000444f00, {0x1058ab5b0, 0x14000382690}, {0x106273480, 0x0, 0x104e54b20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 341
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:37:56 handlers.go:164: TraceID: df0b947c-ce2f-4b08-a4ce-02f437c2007a | Underlying Error: open .config/.profiles: no such file or directory
