[ERROR] 2025/07/04 21:34:23 handlers.go:150: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:23 handlers.go:154: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:159: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Stack Trace:
goroutine 574 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400040f4f0, 0x140002129a0, {0x140002526c0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400040f4f0, {0x103932bd8?, 0x140003043c0?}, {0x140002526c0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400033a1c0?, {0x103932bd8?, 0x140003043c0?}, {0x140002526c0?, 0x1400012e500?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1038daae0?, 0x1400033a1b0?, 0x1400022bc78?}, {0x1032b3126, 0x4}, {0x10430b480, 0x0, 0x102ebf7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1038daae0?, 0x1400033a1b0?, 0x1400022bce8?}, {0x10430b480?, 0x102769f00?, 0x1029faf70?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000687040, {0x1039435b0, 0x1400071b800}, {0x10430b480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 573
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:23 handlers.go:164: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:150: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:23 handlers.go:154: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:159: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Stack Trace:
goroutine 5042 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400060cb90, 0x140002da770, {0x14000370da0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400060cb90, {0x101ad2bd8?, 0x14000387bc0?}, {0x14000370da0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400066c130?, {0x101ad2bd8?, 0x14000387bc0?}, {0x14000370da0?, 0x140002d96c0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400066c120)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400066c120)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x101a7aae0?, 0x1400066c120?, 0x1400021dc78?}, {0x101453126, 0x4}, {0x1024ab480, 0x0, 0x10105f7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101a7aae0?, 0x1400066c120?, 0x100b71780?}, {0x1024ab480?, 0x1400021dd38?, 0x1008e7254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000605d60, {0x101ae35b0, 0x14000447680}, {0x1024ab480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 4993
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:23 handlers.go:164: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:56 handlers.go:150: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:56 handlers.go:154: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:56 handlers.go:159: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Stack Trace:
goroutine 358 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400003c030, 0x140006c8000, {0x140006a0020?, 0x8?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400003c030, {0x103622bd8?, 0x140000c0cc0?}, {0x140006a0020, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000da2e0?, {0x103622bd8?, 0x140000c0cc0?}, {0x140006a0020?, 0x140004407d0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000da2d0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000da2d0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1035caae0?, 0x140000da2d0?, 0x14000093c78?}, {0x102fa3126, 0x4}, {0x103ffb480, 0x0, 0x102baf7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1035caae0?, 0x140000da2d0?, 0x0?}, {0x103ffb480?, 0x14000093d38?, 0x102457028?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140004d0640, {0x1036335b0, 0x1400032fe60}, {0x103ffb480, 0x0, 0x102bdcb20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 357
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:56 handlers.go:164: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:36:37 handlers.go:150: TraceID: 0a846e60-b716-4f0b-b02a-d8bf5437b4c6 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:36:37 handlers.go:154: TraceID: 0a846e60-b716-4f0b-b02a-d8bf5437b4c6 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:36:37 handlers.go:159: TraceID: 0a846e60-b716-4f0b-b02a-d8bf5437b4c6 | Stack Trace:
goroutine 317 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000317120, 0x140005a4070, {0x140002c0140?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000317120, {0x10366ebd8?, 0x140005ab7d0?}, {0x140002c0140, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140001da490?, {0x10366ebd8?, 0x140005ab7d0?}, {0x140002c0140?, 0x140002ec130?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140001da480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140001da480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x103616ae0?, 0x140001da480?, 0x1400049ac78?}, {0x102fef126, 0x4}, {0x104047480, 0x0, 0x102bfb7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x103616ae0?, 0x140001da480?, 0x140001c2780?}, {0x104047480?, 0x1400049ad38?, 0x102483254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000513220, {0x10367f5b0, 0x14000385860}, {0x104047480, 0x0, 0x1?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 297
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:36:37 handlers.go:164: TraceID: 0a846e60-b716-4f0b-b02a-d8bf5437b4c6 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:37:56 handlers.go:150: TraceID: b2644e57-bf41-4767-a5a8-e4974d6e7792 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:37:56 handlers.go:154: TraceID: b2644e57-bf41-4767-a5a8-e4974d6e7792 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:37:56 handlers.go:159: TraceID: b2644e57-bf41-4767-a5a8-e4974d6e7792 | Stack Trace:
goroutine 316 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400029b7a0, 0x14000584380, {0x1400024c880?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400029b7a0, {0x10589abd8?, 0x140003ca240?}, {0x1400024c880, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000ece20?, {0x10589abd8?, 0x140003ca240?}, {0x1400024c880?, 0x140003c2d60?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000ece10)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x105842ae0?, 0x140000ece10?, 0x1400016cc78?}, {0x10521b126, 0x4}, {0x106273480, 0x0, 0x104e277e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105842ae0?, 0x140000ece10?, 0x1400016cdc8?}, {0x106273480?, 0x14000275380?, 0x1055e96a0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000444fa0, {0x1058ab5b0, 0x1400036fcb0}, {0x106273480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 315
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:37:56 handlers.go:164: TraceID: b2644e57-bf41-4767-a5a8-e4974d6e7792 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:37:56 handlers.go:150: TraceID: df0b947c-ce2f-4b08-a4ce-02f437c2007a | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:37:56 handlers.go:154: TraceID: df0b947c-ce2f-4b08-a4ce-02f437c2007a | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:37:56 handlers.go:159: TraceID: df0b947c-ce2f-4b08-a4ce-02f437c2007a | Stack Trace:
goroutine 284 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400029b7a0, 0x1400024ba40, {0x14000362760?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400029b7a0, {0x10589abd8?, 0x140003b5dd0?}, {0x14000362760, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000ece20?, {0x10589abd8?, 0x140003b5dd0?}, {0x14000362760?, 0x140003c2d60?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000ece10)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000ece10)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x105842ae0?, 0x140000ece10?, 0x140005c5c78?}, {0x10521b126, 0x4}, {0x106273480, 0x0, 0x104e277e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105842ae0?, 0x140000ece10?, 0x14000027ce8?}, {0x106273480?, 0x14000027d38?, 0x1046af254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000444f00, {0x1058ab5b0, 0x14000382690}, {0x106273480, 0x0, 0x104e54b20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 341
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:37:56 handlers.go:164: TraceID: df0b947c-ce2f-4b08-a4ce-02f437c2007a | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:38:35 handlers.go:150: TraceID: 0b6f68b2-1c77-48a5-9d0b-1e8c475d30f2 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:38:35 handlers.go:154: TraceID: 0b6f68b2-1c77-48a5-9d0b-1e8c475d30f2 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:38:35 handlers.go:159: TraceID: 0b6f68b2-1c77-48a5-9d0b-1e8c475d30f2 | Stack Trace:
goroutine 419 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400040d380, 0x140005641c0, {0x140006202c0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400040d380, {0x103d72bd8?, 0x140003c9710?}, {0x140006202c0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400033a1c0?, {0x103d72bd8?, 0x140003c9710?}, {0x140006202c0?, 0x140003201d0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x103d1aae0?, 0x1400033a1b0?, 0x1400021cc78?}, {0x1036f3126, 0x4}, {0x10474b480, 0x0, 0x1032ff7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x103d1aae0?, 0x1400033a1b0?, 0x105b1000105a3?}, {0x10474b480?, 0x105bc000105bb?, 0x1073600010600?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000148e60, {0x103d835b0, 0x140003b0120}, {0x10474b480, 0x0, 0x102e35f64?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 241
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:38:35 handlers.go:164: TraceID: 0b6f68b2-1c77-48a5-9d0b-1e8c475d30f2 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:38:35 handlers.go:150: TraceID: 2d2d0827-700f-47a4-892f-4ec8d5d16f3d | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:38:35 handlers.go:154: TraceID: 2d2d0827-700f-47a4-892f-4ec8d5d16f3d | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:38:35 handlers.go:159: TraceID: 2d2d0827-700f-47a4-892f-4ec8d5d16f3d | Stack Trace:
goroutine 362 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400040d380, 0x140005103f0, {0x14000252c40?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400040d380, {0x103d72bd8?, 0x140003c2a80?}, {0x14000252c40, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400033a1c0?, {0x103d72bd8?, 0x140003c2a80?}, {0x14000252c40?, 0x140003201d0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x103d1aae0?, 0x1400033a1b0?, 0x140002c0c78?}, {0x1036f3126, 0x4}, {0x10474b480, 0x0, 0x1032ff7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x103d1aae0?, 0x1400033a1b0?, 0x140002c0d48?}, {0x10474b480?, 0x140002c0d48?, 0x102b88808?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000148dc0, {0x103d835b0, 0x140003a2780}, {0x10474b480, 0x0, 0x1?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 408
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:38:35 handlers.go:164: TraceID: 2d2d0827-700f-47a4-892f-4ec8d5d16f3d | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:41:43 handlers.go:150: TraceID: 75e0c93d-f64a-474c-8d24-0c745ba07e03 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:41:43 handlers.go:154: TraceID: 75e0c93d-f64a-474c-8d24-0c745ba07e03 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:41:43 handlers.go:150: TraceID: b9d85180-85e1-401f-88ba-cdfb7036b723 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:41:43 handlers.go:154: TraceID: b9d85180-85e1-401f-88ba-cdfb7036b723 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:41:43 handlers.go:159: TraceID: 75e0c93d-f64a-474c-8d24-0c745ba07e03 | Stack Trace:
goroutine 376 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400068c010, 0x140001fe070, {0x140002c0160?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400068c010, {0x105976c28?, 0x14000129170?}, {0x140002c0160, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x14000690010?, {0x105976c28?, 0x14000129170?}, {0x140002c0160?, 0x140003ca080?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x14000690000)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x105924460?, 0x14000690000?, 0x1613?}, {0x1052f7a86, 0x4}, {0x106353480, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105924460?, 0x14000690000?, 0x0?}, {0x106353480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000148c80, {0x105987610, 0x140003202d0}, {0x106353480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 375
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:41:43 handlers.go:164: TraceID: 75e0c93d-f64a-474c-8d24-0c745ba07e03 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:41:43 handlers.go:159: TraceID: b9d85180-85e1-401f-88ba-cdfb7036b723 | Stack Trace:
goroutine 388 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400068c010, 0x140001300e0, {0x1400048c280?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400068c010, {0x105976c28?, 0x14000613f50?}, {0x1400048c280, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x14000690010?, {0x105976c28?, 0x14000613f50?}, {0x1400048c280?, 0x140003ca080?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x14000690000)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x14000690000)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x105924460?, 0x14000690000?, 0x1213?}, {0x1052f7a86, 0x4}, {0x106353480, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105924460?, 0x14000690000?, 0x1400007bd08?}, {0x106353480?, 0x140004d4000?, 0x105987648?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000148be0, {0x105987610, 0x14000351140}, {0x106353480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 387
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:41:43 handlers.go:164: TraceID: b9d85180-85e1-401f-88ba-cdfb7036b723 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:42:30 handlers.go:150: TraceID: 9c578890-68f4-4112-b81f-87ec1b0bfb08 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:42:30 handlers.go:154: TraceID: 9c578890-68f4-4112-b81f-87ec1b0bfb08 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:42:30 handlers.go:150: TraceID: 701e0b3b-651a-4d4c-8a0a-f0301028c86a | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:42:30 handlers.go:154: TraceID: 701e0b3b-651a-4d4c-8a0a-f0301028c86a | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:42:30 handlers.go:159: TraceID: 9c578890-68f4-4112-b81f-87ec1b0bfb08 | Stack Trace:
goroutine 386 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000115dd0, 0x1400019a1c0, {0x1400031e520?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000115dd0, {0x105482cc8?, 0x140004f7e30?}, {0x1400031e520, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x14000162490?, {0x105482cc8?, 0x140004f7e30?}, {0x1400031e520?, 0x1400026c650?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x14000162480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x14000162480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x105434cc0?, 0x14000162480?, 0x1213?}, {0x104e03cc6, 0x4}, {0x105e5f480, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105434cc0?, 0x14000162480?, 0x140004e7508?}, {0x105e5f480?, 0x1400031a0c0?, 0x1054936e8?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000289540, {0x1054936b0, 0x140003a2450}, {0x105e5f480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 353
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:42:30 handlers.go:164: TraceID: 9c578890-68f4-4112-b81f-87ec1b0bfb08 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:42:30 handlers.go:159: TraceID: 701e0b3b-651a-4d4c-8a0a-f0301028c86a | Stack Trace:
goroutine 376 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000115dd0, 0x1400023a230, {0x1400027e540?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000115dd0, {0x105482cc8?, 0x140003843c0?}, {0x1400027e540, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x14000162490?, {0x105482cc8?, 0x140003843c0?}, {0x1400027e540?, 0x1400026c650?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x14000162480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x105434cc0?, 0x14000162480?, 0x1a13?}, {0x104e03cc6, 0x4}, {0x105e5f480, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x105434cc0?, 0x14000162480?, 0x1063405c0?}, {0x105e5f480?, 0x14000083808?, 0x14000298e00?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140002897c0, {0x1054936b0, 0x14000304960}, {0x105e5f480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 375
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:42:30 handlers.go:164: TraceID: 701e0b3b-651a-4d4c-8a0a-f0301028c86a | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:43:06 handlers.go:150: TraceID: 3d936db1-4d41-46ab-9498-9360133a3c00 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:43:06 handlers.go:154: TraceID: 3d936db1-4d41-46ab-9498-9360133a3c00 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:43:06 handlers.go:159: TraceID: 3d936db1-4d41-46ab-9498-9360133a3c00 | Stack Trace:
goroutine 421 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000308f20, 0x140002bc070, {0x1400024e2e0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000308f20, {0x1034b6cc8?, 0x14000398c90?}, {0x1400024e2e0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400033a1c0?, {0x1034b6cc8?, 0x14000398c90?}, {0x1400024e2e0?, 0x14000010960?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x103468cc0?, 0x1400033a1b0?, 0x1213?}, {0x102e37cc6, 0x4}, {0x103e93480, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x103468cc0?, 0x1400033a1b0?, 0x0?}, {0x103e93480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140004c5220, {0x1034c76b0, 0x1400017f920}, {0x103e93480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 420
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:43:06 handlers.go:164: TraceID: 3d936db1-4d41-46ab-9498-9360133a3c00 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:43:57 handlers.go:150: TraceID: 8822c98c-6960-4659-921b-2e3bd3f76e11 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:43:57 handlers.go:154: TraceID: 8822c98c-6960-4659-921b-2e3bd3f76e11 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:43:57 handlers.go:159: TraceID: 8822c98c-6960-4659-921b-2e3bd3f76e11 | Stack Trace:
goroutine 391 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400003d0a0, 0x140002ca770, {0x140001abac0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400003d0a0, {0x105506cc8?, 0x14000320270?}, {0x140001abac0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000d4400?, {0x105506cc8?, 0x14000320270?}, {0x140001abac0?, 0x14000680470?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000d43f0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x1054b8cc0?, 0x140000d43f0?, 0x1400043fc78?}, {0x104e87cc6, 0x4}, {0x105ee3480, 0x0, 0x104a937e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1054b8cc0?, 0x140000d43f0?, 0x1045a5780?}, {0x105ee3480?, 0x1400043fd38?, 0x10431b254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000493d60, {0x1055176b0, 0x140003200c0}, {0x105ee3480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 390
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:43:57 handlers.go:150: TraceID: d4001d98-8d2a-45b6-b78c-0b165f9082d0 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:43:57 handlers.go:154: TraceID: d4001d98-8d2a-45b6-b78c-0b165f9082d0 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:43:57 handlers.go:164: TraceID: 8822c98c-6960-4659-921b-2e3bd3f76e11 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:43:57 handlers.go:159: TraceID: d4001d98-8d2a-45b6-b78c-0b165f9082d0 | Stack Trace:
goroutine 321 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400003d0a0, 0x140002b27e0, {0x140001328a0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400003d0a0, {0x105506cc8?, 0x14000336840?}, {0x140001328a0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000d4400?, {0x105506cc8?, 0x14000336840?}, {0x140001328a0?, 0x14000680470?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000d43f0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000d43f0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1054b8cc0?, 0x140000d43f0?, 0x140004dfc78?}, {0x104e87cc6, 0x4}, {0x105ee3480, 0x0, 0x104a937e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1054b8cc0?, 0x140000d43f0?, 0x202c6174656d2873?}, {0x105ee3480?, 0x202020200a3b5d30?, 0x6c65282066692020?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000493b80, {0x1055176b0, 0x14000431dd0}, {0x105ee3480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 320
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:43:57 handlers.go:164: TraceID: d4001d98-8d2a-45b6-b78c-0b165f9082d0 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:45:07 handlers.go:150: TraceID: a6e31e7e-0cd5-4f52-a666-7177316cf4ee | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:45:07 handlers.go:154: TraceID: a6e31e7e-0cd5-4f52-a666-7177316cf4ee | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:45:07 handlers.go:150: TraceID: 737cf741-37e3-4636-a61d-8fbca08a74be | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:45:07 handlers.go:154: TraceID: 737cf741-37e3-4636-a61d-8fbca08a74be | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:45:07 handlers.go:159: TraceID: 737cf741-37e3-4636-a61d-8fbca08a74be | Stack Trace:
goroutine 354 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000309a40, 0x1400020c8c0, {0x1400027e7a0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000309a40, {0x101452cc8?, 0x140003a2270?}, {0x1400027e7a0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400033a490?, {0x101452cc8?, 0x140003a2270?}, {0x1400027e7a0?, 0x1400070a630?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400033a480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400033a480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x101404cc0?, 0x1400033a480?, 0x14000093c78?}, {0x100dd3cc6, 0x4}, {0x101e2f480, 0x0, 0x1009df7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101404cc0?, 0x1400033a480?, 0x0?}, {0x101e2f480?, 0x14000093d38?, 0x100267254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400044d400, {0x1014636b0, 0x14000381740}, {0x101e2f480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 337
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:45:07 handlers.go:164: TraceID: 737cf741-37e3-4636-a61d-8fbca08a74be | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:45:07 handlers.go:159: TraceID: a6e31e7e-0cd5-4f52-a666-7177316cf4ee | Stack Trace:
goroutine 372 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000309a40, 0x1400019a5b0, {0x1400024a3c0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000309a40, {0x101452cc8?, 0x140003946c0?}, {0x1400024a3c0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400033a490?, {0x101452cc8?, 0x140003946c0?}, {0x1400024a3c0?, 0x1400070a630?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400033a480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x101404cc0?, 0x1400033a480?, 0x140004eac78?}, {0x100dd3cc6, 0x4}, {0x101e2f480, 0x0, 0x1009df7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101404cc0?, 0x1400033a480?, 0x140004eace8?}, {0x101e2f480?, 0x0?, 0x10051af70?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x1400044d5e0, {0x1014636b0, 0x140000cd320}, {0x101e2f480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 371
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:45:07 handlers.go:164: TraceID: a6e31e7e-0cd5-4f52-a666-7177316cf4ee | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:47:10 handlers.go:150: TraceID: 749aa314-e560-4b71-aa38-b6a4a1cb6266 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:47:10 handlers.go:154: TraceID: 749aa314-e560-4b71-aa38-b6a4a1cb6266 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:47:10 handlers.go:150: TraceID: 10d78e53-0f5b-4550-afab-deda9e4cadac | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:47:10 handlers.go:154: TraceID: 10d78e53-0f5b-4550-afab-deda9e4cadac | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:47:10 handlers.go:159: TraceID: 749aa314-e560-4b71-aa38-b6a4a1cb6266 | Stack Trace:
goroutine 396 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x140003170d0, 0x140000ba620, {0x14000111a40?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x140003170d0, {0x10151acc8?, 0x140004727e0?}, {0x14000111a40, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x14000162490?, {0x10151acc8?, 0x140004727e0?}, {0x14000111a40?, 0x14000010610?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x14000162480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x0?)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:184 +0x24
reflect.Value.call({0x1014cccc0?, 0x14000162480?, 0x14000159c78?}, {0x100e9bd22, 0x4}, {0x101ef7480, 0x0, 0x100aa7844?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1014cccc0?, 0x14000162480?, 0x1005b9780?}, {0x101ef7480?, 0x14000159d38?, 0x10032f254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000206640, {0x10152b6b0, 0x1400044e810}, {0x101ef7480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 395
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:47:10 handlers.go:164: TraceID: 749aa314-e560-4b71-aa38-b6a4a1cb6266 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:47:10 handlers.go:159: TraceID: 10d78e53-0f5b-4550-afab-deda9e4cadac | Stack Trace:
goroutine 303 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x140003170d0, 0x1400023b1f0, {0x140004fa4c0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x140003170d0, {0x10151acc8?, 0x140004610b0?}, {0x140004fa4c0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x14000162490?, {0x10151acc8?, 0x140004610b0?}, {0x140004fa4c0?, 0x14000010610?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x14000162480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x14000162480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1014cccc0?, 0x14000162480?, 0x1400015ac78?}, {0x100e9bd22, 0x4}, {0x101ef7480, 0x0, 0x100aa7844?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1014cccc0?, 0x14000162480?, 0x1400015ace8?}, {0x101ef7480?, 0x1002f8b00?, 0x1005e2f70?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000206140, {0x10152b6b0, 0x140004529f0}, {0x101ef7480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 302
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:47:10 handlers.go:164: TraceID: 10d78e53-0f5b-4550-afab-deda9e4cadac | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:48:32 handlers.go:150: TraceID: 0e467f56-5e15-4bb4-96b6-afb8610f7816 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:48:32 handlers.go:154: TraceID: 0e467f56-5e15-4bb4-96b6-afb8610f7816 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:48:32 handlers.go:150: TraceID: 4db72d01-a649-4873-8cbd-c949122fe178 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:48:32 handlers.go:154: TraceID: 4db72d01-a649-4873-8cbd-c949122fe178 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:48:32 handlers.go:159: TraceID: 0e467f56-5e15-4bb4-96b6-afb8610f7816 | Stack Trace:
goroutine 299 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x140003293b0, 0x140002c2310, {0x140001ab9c0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x140003293b0, {0x101b8ecc8?, 0x140003c6750?}, {0x140001ab9c0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140001da490?, {0x101b8ecc8?, 0x140003c6750?}, {0x140001ab9c0?, 0x14000138270?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140001da480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x140001da480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:185 +0x78
reflect.Value.call({0x101b40cc0?, 0x140001da480?, 0x14000613c78?}, {0x101510282, 0x4}, {0x10256b480, 0x0, 0x10111b844?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101b40cc0?, 0x140001da480?, 0x0?}, {0x10256b480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000517ea0, {0x101b9f6b0, 0x140003bc9c0}, {0x10256b480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 298
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:48:32 handlers.go:164: TraceID: 0e467f56-5e15-4bb4-96b6-afb8610f7816 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:48:32 handlers.go:159: TraceID: 4db72d01-a649-4873-8cbd-c949122fe178 | Stack Trace:
goroutine 200 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x140003293b0, 0x14000284150, {0x140002c0500?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x140003293b0, {0x101b8ecc8?, 0x140003b7e60?}, {0x140002c0500, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140001da490?, {0x101b8ecc8?, 0x140003b7e60?}, {0x140002c0500?, 0x14000138270?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140001da480)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140001da480)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x101b40cc0?, 0x140001da480?, 0x140005dec78?}, {0x101510282, 0x4}, {0x10256b480, 0x0, 0x10111b844?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101b40cc0?, 0x140001da480?, 0x0?}, {0x10256b480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000517c20, {0x101b9f6b0, 0x140003a8270}, {0x10256b480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 199
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:48:32 handlers.go:164: TraceID: 4db72d01-a649-4873-8cbd-c949122fe178 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:50:11 handlers.go:150: TraceID: c18acd5b-fd7b-456f-a4de-788cf46b8fd6 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:50:11 handlers.go:154: TraceID: c18acd5b-fd7b-456f-a4de-788cf46b8fd6 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:50:11 handlers.go:150: TraceID: 748f608d-a1eb-407c-87bf-99b6161bea62 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:50:11 handlers.go:154: TraceID: 748f608d-a1eb-407c-87bf-99b6161bea62 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:50:11 handlers.go:159: TraceID: c18acd5b-fd7b-456f-a4de-788cf46b8fd6 | Stack Trace:
goroutine 240 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x140004bc620, 0x140001fe070, {0x14000518160?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x140004bc620, {0x103acacc8?, 0x140004bf290?}, {0x14000518160, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140004a00a0?, {0x103acacc8?, 0x140004bf290?}, {0x14000518160?, 0x14000488360?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140004a0090)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140004a0090)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x103a7ccc0?, 0x140004a0090?, 0x1213?}, {0x10344c282, 0x4}, {0x1044a7480, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x103a7ccc0?, 0x140004a0090?, 0x0?}, {0x1044a7480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140004baaa0, {0x103adb6b0, 0x1400033fa40}, {0x1044a7480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 239
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:50:11 handlers.go:164: TraceID: c18acd5b-fd7b-456f-a4de-788cf46b8fd6 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:50:11 handlers.go:159: TraceID: 748f608d-a1eb-407c-87bf-99b6161bea62 | Stack Trace:
goroutine 371 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x140004bc620, 0x140002be0e0, {0x140004761e0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x140004bc620, {0x103acacc8?, 0x140003420c0?}, {0x140004761e0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140004a00a0?, {0x103acacc8?, 0x140003420c0?}, {0x140004761e0?, 0x14000488360?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140004a0090)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetRemotes(0x140004a0090)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:185 +0x78
reflect.Value.call({0x103a7ccc0?, 0x140004a0090?, 0x1a13?}, {0x10344c282, 0x4}, {0x1044a7480, 0x0, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x103a7ccc0?, 0x140004a0090?, 0x0?}, {0x1044a7480?, 0x0?, 0x0?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140004bac80, {0x103adb6b0, 0x1400036acf0}, {0x1044a7480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 370
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:50:11 handlers.go:164: TraceID: 748f608d-a1eb-407c-87bf-99b6161bea62 | Underlying Error: open .config/.profiles: no such file or directory
