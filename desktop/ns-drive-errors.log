[ERROR] 2025/07/04 21:34:23 handlers.go:150: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:23 handlers.go:154: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:159: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Stack Trace:
goroutine 574 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400040f4f0, 0x140002129a0, {0x140002526c0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400040f4f0, {0x103932bd8?, 0x140003043c0?}, {0x140002526c0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400033a1c0?, {0x103932bd8?, 0x140003043c0?}, {0x140002526c0?, 0x1400012e500?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400033a1b0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1038daae0?, 0x1400033a1b0?, 0x1400022bc78?}, {0x1032b3126, 0x4}, {0x10430b480, 0x0, 0x102ebf7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1038daae0?, 0x1400033a1b0?, 0x1400022bce8?}, {0x10430b480?, 0x102769f00?, 0x1029faf70?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000687040, {0x1039435b0, 0x1400071b800}, {0x10430b480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 573
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:23 handlers.go:164: TraceID: 83337aeb-a36b-4864-811b-03e49b93e79e | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:150: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:23 handlers.go:154: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:23 handlers.go:159: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Stack Trace:
goroutine 5042 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400060cb90, 0x140002da770, {0x14000370da0?, 0x0?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400060cb90, {0x101ad2bd8?, 0x14000387bc0?}, {0x14000370da0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x1400066c130?, {0x101ad2bd8?, 0x14000387bc0?}, {0x14000370da0?, 0x140002d96c0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x1400066c120)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x1400066c120)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x101a7aae0?, 0x1400066c120?, 0x1400021dc78?}, {0x101453126, 0x4}, {0x1024ab480, 0x0, 0x10105f7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x101a7aae0?, 0x1400066c120?, 0x100b71780?}, {0x1024ab480?, 0x1400021dd38?, 0x1008e7254?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x14000605d60, {0x101ae35b0, 0x14000447680}, {0x1024ab480, 0x0, 0x20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 4993
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:23 handlers.go:164: TraceID: 53fe2733-5a87-425e-996d-994201fa99a5 | Underlying Error: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:56 handlers.go:150: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Code: NOT_FOUND_ERROR | Message: Resource not found [Context: [init_config load_profiles]]
[ERROR] 2025/07/04 21:34:56 handlers.go:154: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Details: open .config/.profiles: no such file or directory
[ERROR] 2025/07/04 21:34:56 handlers.go:159: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Stack Trace:
goroutine 358 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x1400003c030, 0x140006c8000, {0x140006a0020?, 0x8?, 0x0?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x1400003c030, {0x103622bd8?, 0x140000c0cc0?}, {0x140006a0020, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleError(0x140000da2e0?, {0x103622bd8?, 0x140000c0cc0?}, {0x140006a0020?, 0x140004407d0?, 0xf?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:91 +0x3c
desktop/backend.(*App).initializeConfig(0x140000da2d0)
	/Users/<USER>/Github/ns-drive/desktop/backend/app.go:130 +0x214
desktop/backend.(*App).GetConfigInfo(0x140000da2d0)
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:157 +0x44
reflect.Value.call({0x1035caae0?, 0x140000da2d0?, 0x14000093c78?}, {0x102fa3126, 0x4}, {0x103ffb480, 0x0, 0x102baf7e4?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:584 +0x978
reflect.Value.Call({0x1035caae0?, 0x140000da2d0?, 0x0?}, {0x103ffb480?, 0x14000093d38?, 0x102457028?})
	/Users/<USER>/.goenv/versions/1.24.2/src/reflect/value.go:368 +0x94
github.com/wailsapp/wails/v3/pkg/application.(*BoundMethod).Call(0x140004d0640, {0x1036335b0, 0x1400032fe60}, {0x103ffb480, 0x0, 0x102bdcb20?})
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/bindings.go:337 +0x44c
github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod.func1()
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:117 +0xa0
created by github.com/wailsapp/wails/v3/pkg/application.(*MessageProcessor).processCallMethod in goroutine 357
	/Users/<USER>/go/1.24.2/pkg/mod/github.com/wailsapp/wails/v3@v3.0.0-alpha.9/pkg/application/messageprocessor_call.go:108 +0x440
[ERROR] 2025/07/04 21:34:56 handlers.go:164: TraceID: 82c3a864-d810-451c-936b-fd7bfa1e0018 | Underlying Error: open .config/.profiles: no such file or directory
